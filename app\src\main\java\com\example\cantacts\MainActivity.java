package com.example.cantacts;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.os.Build;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.util.Log;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;
public class MainActivity extends AppCompatActivity {
    private ContactAdapter adapter;
    private RecyclerView rv_contact;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        init();

    }
    private void setData(){
        List<ContactInfo> contactInfos=getContacts();
        adapter=new ContactAdapter(MainActivity.this,contactInfos);
        rv_contact.setAdapter(adapter);
    }
    public List<ContactInfo> getContacts() {
        List<ContactInfo> contactInfos = new ArrayList<>();
        Cursor cursor = getContentResolver().query(ContactsContract.
                Contacts.CONTENT_URI, null, null, null, null);
        if (contactInfos!=null)contactInfos.clear();//清除集合中的数据
        while (cursor.moveToNext()) {
// 1. 安全获取列索引，如果不存在则跳过当前联系人
            int idColumnIndex =
                    cursor.getColumnIndex(ContactsContract.Contacts._ID);
            if (idColumnIndex < 0) {
                Log.w("ContactQuery", "_ID 列不存在");
                continue;
            }
            int nameColumnIndex =
                    cursor.getColumnIndex(ContactsContract.Contacts.DISPLAY_NAME);
            if (nameColumnIndex < 0) {
                Log.w("ContactQuery", "DISPLAY_NAME 列不存在");
                continue;
            }
            int hasPhoneColumnIndex =
                    cursor.getColumnIndex(ContactsContract.Contacts.HAS_PHONE_NUMBER);
            if (hasPhoneColumnIndex < 0) {
                Log.w("ContactQuery", "HAS_PHONE_NUMBER 列不存在");
                continue;
            }
// 2. 获取基本联系人信息
            String id = cursor.getString(idColumnIndex);
            String name = cursor.getString(nameColumnIndex);
            try {
// 3. 检查是否有电话号码
                int hasPhone = cursor.getInt(hasPhoneColumnIndex);
                //更安全的获取方式
                if (hasPhone > 0) {
// 4. 查询电话号码（使用参数化查询防止SQL注入）
                    Cursor phoneCursor =
                            getContentResolver().query(ContactsContract.CommonDataKinds.Phone.CONTENT_URI, null,
                                    ContactsContract.CommonDataKinds.Phone.CONTACT_ID + " = ?",
                                    new String[]{id},  // 参数化查询更安全
                                    null);
                    if (phoneCursor != null) {
                        try {
                            int phoneNumberColumnIndex =
                                    phoneCursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER);
                            if (phoneNumberColumnIndex >= 0) {
                                while (phoneCursor.moveToNext()) {
                                    ContactInfo info = new ContactInfo();
                                    info.setContactName(name);
// 格式化电话号码
                                    String number =
                                            phoneCursor.getString(phoneNumberColumnIndex)
                                                    .trim().replace(" ", "").replace("-", "");
                                    info.setPhoneNumber(number);
                                    contactInfos.add(info);
                                }
                            } else {
                                Log.w("ContactQuery", "NUMBER 列不存在");
                            }
                        } finally {
                            phoneCursor.close(); // 确保关闭Cursor
                        }
                    }
                }
            } catch (Exception e) {
                Log.e("ContactQuery", "处理联系人时出错: " +
                        e.getMessage());
            }
        }

        cursor.close();
        return contactInfos;
    }

    private void init(){
        rv_contact=findViewById(R.id.rv_contact);
        rv_contact.setLayoutManager(new LinearLayoutManager(this));
        getPermissions();
    }
    String[] permissionList;
    public void getPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            permissionList = new
                    String[]{"android.permission.READ_CONTACTS"};
            ArrayList<String> list = new ArrayList<String>();
// 循环判断所需权限中有哪个尚未被授权
            for (int i = 0; i < permissionList.length; i++) {
                if (ActivityCompat.checkSelfPermission(this,
                        permissionList[i])
                        != PackageManager.PERMISSION_GRANTED)
                    list.add(permissionList[i]);
            }
            if (list.size() > 0) {
                ActivityCompat.requestPermissions(this,
                        list.toArray(new String[list.size()]), 1);
            } else {
                setData();//后续创建该方法
            }
        } else {
            setData();  //后续创建该方法
        }
    }
    @Override
    public void onRequestPermissionsResult(int requestCode, String[]
                                                   permissions,
                                           int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions,
                grantResults);
        if (requestCode == 1) {
            for (int i = 0; i < permissions.length; i++) {
                if(permissions[i].equals("android.permission.READ_CONTACTS")
                        && grantResults[i] ==
                        PackageManager.PERMISSION_GRANTED){
                    Toast.makeText(this, "读取通讯录权限申请成功",
                            Toast.LENGTH_SHORT).show();
                    setData();//后续创建该方法
                }else{
                    Toast.makeText(this,"读取通讯录权限申请失败",
                            Toast.LENGTH_SHORT).show();
                }
            }
        }    }      }